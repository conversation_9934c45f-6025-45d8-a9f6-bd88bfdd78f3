package services

import (
	"context"
	repositories "smart-city/internal/app/user/repository"
	"smart-city/internal/models"
)

type Service struct {
	userRepo repositories.UserRepository
}

func NewUserService(userRepo repositories.UserRepository) *Service {
	return &Service{userRepo: userRepo}
}

func (s *Service) CreateUser(ctx context.Context, user *models.User) (*models.User, error) {
	return s.userRepo.CreateUser(ctx, user)
}

func (s *Service) GetUsers(ctx context.Context) ([]models.User, error) {
	return s.userRepo.GetUsers(ctx)
}
